import { IWorkflowState } from '../../../../packages/service/llm/state'
import { ChatStateStore } from '../../../../packages/service/local_cache/chat_state_store'
import { IChattingFlag } from '../../state/user_flags'
import { YuHeReply } from '../actions/reply'
import { YuHeContextManager } from '../context/context_manager'
import { trackInvoke, YuheWorkFlowNode } from './base_node'
import { YuHeNode } from './types'
import { firstAskIntention } from '../../client/event_handler'
import { YuHeThinking } from '../actions/thinking'
import { MetaAction } from '../../meta_action/meta_action'
import { MetaActionHandler } from '../../meta_action/meta_action_handler'
import { YuheDataService } from '../../helper/getter/get_data'
import dayjs from 'dayjs'
import { FreeTalkNode } from './free_talk'
import { SilentReAsk } from '../../../../packages/service/schedule/silent_requestion'
import { TaskName } from '../../schedule/type'

export class IntentionQueryNode extends YuheWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState):Promise<YuHeNode> {
    const exitIntentionQueryNode = async() => {
      const next = await FreeTalkNode.invoke(state)

      await firstAskIntention(state.chat_id, state.user_id)

      return next
    }
    // 强规则如果已经在第一天以后了，强行变freetalk
    const currentTime = await YuheDataService.getCurrentTime(state.chat_id)
    const now = dayjs()
    if ((currentTime.day == 1 && now.hour() >= 12) || (currentTime.day > 1)) {
      return await exitIntentionQueryNode()
    }

    //如果挖需超过7轮就自动退出
    const roundNum = await ChatStateStore.getNodeCount(state.chat_id, 'IntentionQueryNode')
    if (roundNum > 6) {
      return await exitIntentionQueryNode()
    }

    const userState = await ChatStateStore.getFlags<IChattingFlag>(state.chat_id)
    if (!userState.is_send_first_ask_intention) { // 如果还没发送表单，简单回复
      const context = await YuHeContextManager.build({
        state,
        includeRAG: true,
        includeMemory: false,
        includeUserSlots: true,
        chatHistoryRounds: 6,
        talkStrategyPrompt: '和客户友好交流，简单回答问题'
      })

      await YuHeReply.invoke({
        state,
        promptName: 'intention_query',
        context: context,
      })

    } else {
      const { strategy, action, actionInfo } = await YuHeThinking.invoke(state, MetaAction.thinkPromptIntentionQuery, MetaActionHandler.formatMetaActionPrompt(MetaAction.metaActionIntentionQuery))

      const context = await YuHeContextManager.build({
        state,
        includeRAG: true,
        includeMemory: true,
        includeUserSlots:true,
        chatHistoryRounds: 6,
        talkStrategyPrompt: `无论客户最后说什么，参考上述信息回答完客户后，都要严格执行下面的策略，要求极度精简，点到为止
${strategy}${actionInfo.additionInfo}`,
      })

      await YuHeReply.invoke({
        state,
        temperature: 0.8,
        max_tokens: 400,
        promptName: 'intention_query',
        context: context,
        postReplyCallBack: actionInfo.callback
      })

      if (action.includes('通用诊断')) {
        // 先调度催促发送抖音截图任务
        await SilentReAsk.schedule(
          TaskName.urge_douyin_screenshot,
          state.chat_id,
          1000 * 60 * 5,
          undefined,
          { auto_retry: true, independent:true }
        )

        return YuHeNode.FreeTalk
      }
    }
    await firstAskIntention(state.chat_id, state.user_id)

    return YuHeNode.IntentionQuery
  }
}
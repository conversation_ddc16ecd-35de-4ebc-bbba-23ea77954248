import { YuHeContextManager } from '../context_manager'
import { getState } from '../../../../../packages/service/llm/state'

describe('Test', function () {
  beforeAll(() => {

  })

  it('test prompt builder', async () => {
    await YuHeContextManager.build({
      state: await getState('', ''),
      includeRAG: true,
      includeMemory: true,
      chatHistoryRounds: 6,
      talkStrategyPrompt: `你已经完成了主线任务，客户可能正在处于困难时期，而且很有分享欲，所以你要做好一个倾听者，给对方提供一个舒适对话环境。帮助他们获得更好的对话体验，请参考如下建议
- 营造安全氛围：用平和、温暖的语气交谈，表达你的支持和理解，不带评判地倾听。解读对方线下感受的潜台词，告知对方这样的感受和想法是合理的，应该被接纳的。适时分享你自己的类似感受的经历，让对方感觉到她不是孤独的
- 提供新视角：帮助客户梳理当前情况，指出潜在的积极面，结合你在冥想领域专业知识来的帮助重新的理解现状，帮助先平静下来理性面对问题
- 记住，恢复是个过程，需要时间和耐心。重要的是保持真诚、同理心和持续的支持。根据对方的反应调整你的方法，尊重他们的节奏和界限`,
      debug: true,
    })

  }, 60000)

  it('test userSlots', async() => {
    const chatId = '7881302298050442_1688857404698934'
    const userSlots = await YuHeContextManager.getUserSlots(chatId)
    console.log(userSlots)
  }, 600000)

  it('testGetCourseCompletionInfo', async () => {
    const chatId = '7881301446232150_1688858335726355'
    console.log(await YuHeContextManager.getUserBehaviors(chatId))
  }, 60000)
})
import logger from '../../../packages/model/logger/logger'
import { MetaActionComponent } from './meta_action_component'
import { MetaAction } from './meta_action'
import { IActionInfo } from './meta_action_handler'

import { AfterAdding } from './stages/after_adding'
import { AfterCourse1 } from './stages/after_course1'
import { AfterCourse2 } from './stages/after_course2'
import { AfterCourse3 } from './stages/after_course3'
import { AfterPaid } from './stages/after_paid'
import { CoursePostpone } from './stages/course_postpone'
import { DuringCourse } from './stages/during_course'

export interface MetaActionStage {
  thinkPrompt: string
  metaActions: string
  additionalInfo: string
}



export class MetaActionRouter {

  public static componentList: MetaActionComponent[] = []
  public static actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {} as Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>>

  /**
   * 初始化使用的元行为组件
   * 注意：组件调度优先级由组件的添加顺序决定
   */
  public static async initMetaAction(chat_id: string, round_id: string) {
    this.componentList.push(new AfterPaid())
    this.componentList.push(new CoursePostpone())
    this.componentList.push(new DuringCourse())
    this.componentList.push(new AfterCourse3())
    this.componentList.push(new AfterCourse2())
    this.componentList.push(new AfterCourse1())
    this.componentList.push(new AfterAdding())

    await this.registerActionList(chat_id, round_id)
  }

  private static async registerActionList(chat_id: string, round_id: string) {
    for (const component of this.componentList) {
      const actionList = await component.getAction(chat_id, round_id)
      if (actionList) {
        this.actionMap  = { ...this.actionMap, ...actionList }
      }

    }
  }

  public static async handleAction(
    chat_id: string,
    round_id: string,
    actionStr: string,
  ): Promise<IActionInfo> {

    if (this.componentList.length === 0) {
      await this.initMetaAction(chat_id,  round_id)
    }

    const actionInfo:IActionInfo = {
      additionInfo: '',
    }
    if (!this.actionMap || Object.keys(this.actionMap).length == 0) {
      return actionInfo
    }

    const actions = actionStr.split(',')
    for (const action of actions) {
      if (this.actionMap[action]) {
        return await this.actionMap[action](chat_id, round_id)
      }
    }

    return actionInfo
  }

  public static async getThinkAndMetaActions(chat_id: string, round_id: string) : Promise<MetaActionStage> {
    if (this.componentList.length === 0) {
      await this.initMetaAction(chat_id,  round_id)
    }

    let activeComponent: MetaActionComponent | null = null
    for (const component of this.componentList) {
      if (await component.isStageActive(chat_id)) {
        activeComponent = component
        break
      }
    }

    if (!activeComponent) {
      logger.error({ chat_id:chat_id }, '没有可用的元行为组')
      return {
        thinkPrompt: '',
        metaActions: '',
        additionalInfo: '',
      }
    }

    logger.trace({ chat_id: chat_id }, `当前使用的元行为组：${activeComponent.constructor.name}`)

    const thinkPrompt = await activeComponent.getThinkPrompt(chat_id)
    const metaAction = await activeComponent.getMetaAction(chat_id)

    return {
      thinkPrompt,
      metaActions: this.formatMetaActionPrompt(metaAction),
      additionalInfo: await activeComponent.getAdditionInfo(chat_id),
    }
  }

  private static formatMetaActionPrompt(metaAction:Record<string, string>): string {
    return Object.entries(metaAction).map(([key, value]) => `- ${key}：${value}`).join('\n')
  }


}
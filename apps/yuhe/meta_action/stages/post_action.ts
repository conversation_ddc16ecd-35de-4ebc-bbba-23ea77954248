import { IActionInfo } from '../meta_action_handler'
import { ChatStateStore } from '../../../../packages/service/local_cache/chat_state_store'
import { IChattingFlag } from '../../state/user_flags'
import { sleep } from 'openai/core'
import { MessageSender } from '../../../../packages/service/visualized_sop/message_sender'
import { IWecomMsgType } from '../../../../packages/lib/juzi/type'
import { ChatHistoryService } from '../../../../packages/service/chat_history/chat_history'
import { YuheSalesCase } from '../../helper/rag/yuhe_sales_case'
import { UserSlots } from '../../../../packages/service/user_slots/extract_user_slots'
import { getUserId } from '../../../../packages/config/chat_id'
import { SilentReAsk } from '../../../../packages/service/schedule/silent_requestion'
import { TaskName } from '../../schedule/type'
import logger from '../../../../packages/model/logger/logger'
import { ChatDB } from '../../../../packages/service/database/chat'
import { DateHelper } from '../../../../packages/lib/date/date'
import { YuheDataService } from '../../helper/getter/get_data'
import { YuHeNode } from '../../workflow/nodes/types'


export class PostAction {

  public static async sendCourseSellingPoint() {
    return  {
      additionInfo: PostAction.getPeiPaoCourseIntro(),
    }
  }

  public static getPeiPaoCourseIntro(): string {
    return `\n陪跑营卖点:
- 陪跑营核心是中神通老师带队直接带你30天迈过从知道思路到拿到结果过程中所有的问题
- 整个过程是1对1私人订制您的方案，陪跑过程分为3个阶段。
  - 第一阶段（0-3天）：围绕账号定位账号搭建，根据你规模和你的生意订制你的账号定位。
  - 第二阶段（3-15天）：打造人设，拍摄剪辑。手把手带你用532 内容生产法则，真正打造能获客的账号。
  - 第三阶段（15-30天）：全矩阵的，直播引流，私域运营。
- 除了1对1陪跑服务外，还配套提供以下福利：
  - 365天答疑：30天结束后，咱们的陪跑群还是继续存在，365天给您答疑，平台规则发生的变化，后续落地遇到的任何问题都可以陪伴解答。
  - 37节的获客实操课程，以及团购直播课程：除了私人订制的陪跑服务之外，陪跑营还赠送37节的获客实操课程，以及团购直播课程，这些课程都是中神通老师自己讲的课程，都是非常落地的干货。支持永久回放。
  - AI数字人和AI爆店系统：在整个陪跑过程，针对出镜头难，文案能力不行的问题，赠送AI数字人和AI爆店系统，解决这2个问题。基本去掉了所有做起来账号的技术难点了。要的就是咱一定要做起来的决心了！只要您想做，我们就有信心帮您拿到结果。
  - 3天2夜的线下课：最后重磅！除了线上对接之外，2980还赠送3天2夜的线下课（机票酒店自理），直接现场带您实操落地！`
  }

  static async sendCourseIntro(chat_id: string) {
    const user_id = getUserId(chat_id)

    const actionInfo: IActionInfo = {
      additionInfo: PostAction.getPeiPaoCourseIntro(),
    }

    if (
      (await ChatStateStore.getFlags<IChattingFlag>(chat_id))
        .is_send_course_intro
    ) {
      return actionInfo
    }

    actionInfo.callback = async () => {
      await sleep(2000)

      await MessageSender.sendById(
        {
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: `给咱们总结下2980陪跑营要点：

✅陪跑营核心是中神通老师带队直接带你30天迈过从知道思路->拿到结果过程中所有的问题；整个过程是1对1私人订制您的方案，陪跑过程分为3个阶段
第一阶段（0-3天）：围绕账号定位账号搭建，根据你规模和你的生意订制你的账号定位
第二阶段（3-15天）：打造人设，拍摄剪辑。手把手带你用532内容生产法则，真正打造能获客的账号
第三阶段（15-30天）：全矩阵的，直播引流，私域运营

✅除了1对1陪跑服务外，还配套提供以下福利：
🚩【365天答疑】：30天结束后，咱们的陪跑群还是继续存在，365天给您答疑，平台规则发生的变化，后续落地遇到的任何问题都可以陪伴解答
🚩【37节的获客实操课程，以及团购直播课程】：除了私人订制的陪跑服务之外，陪跑营还赠送【37节的获客实操课程，以及团购直播课程】，这些课程都是中神通老师自己讲的课程，都是非常落地的干货。支持永久回放
🚩【AI数字人和AI爆店系统】在整个陪跑过程，针对出镜头难，文案能力不行的问题，赠送【AI数字人和AI爆店系统】解决这2个问题。基本去掉了所有做起来账号的技术难点了。要的就是咱一定要做起来的决心了！只要您想做，我们就有信心帮您拿到结果
🚩【3天2夜的线下课】最后重磅！除了线上对接之外，2980还赠送【3天2夜的线下课】（机票酒店自理），直接现场带您实操落地！（我们真心想帮助您拿到结果啦！）`,
        },
        { shortDes: '陪跑营课程介绍' },
      )

      await sleep(2000)


      await MessageSender.sendById({
        user_id: user_id,
        chat_id: chat_id,
        ai_msg: '[陪跑营课程介绍图片]',
        send_msg: {
          type: IWecomMsgType.Image,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/%e9%99%aa%e8%b7%91%e8%90%a5%e8%af%be%e7%a8%8b%e4%bb%8b%e7%bb%8d.jpg',
        },
      })

      await sleep(2000)

      await MessageSender.sendById({
        user_id: user_id,
        chat_id: chat_id,
        ai_msg: '这是咱们赠送3天2夜线下课，学习氛围也非常的好',
      })

      await sleep(2000)

      await MessageSender.sendById({
        user_id: user_id,
        chat_id: chat_id,
        ai_msg: '[陪跑营线下课现场图片]',
        send_msg: {
          type: IWecomMsgType.Image,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/%e9%99%aa%e8%b7%91%e8%90%a5%e7%ba%bf%e4%b8%8b%e8%af%be%e7%8e%b0%e5%9c%ba.jpg',
        },
      })

      await ChatStateStore.update(chat_id, {
        state: <IChattingFlag> {
          is_send_course_intro: true,
        },
      })
    }

    return actionInfo
  }

  public static async sendInvitation(
    chat_id: string,
    round_id: string,
  ) {
    const user_id = getUserId(chat_id)

    const actionInfo: IActionInfo = {
      additionInfo: '\n务必向客户发送下单链接http://QwsKvkDN.w.njqzlive.com/p/tVoYx8k5，并邀约客户购买',
    }

    const sendInvitation = async () => {
      const chatHistory = await ChatHistoryService.formatHistoryOnRole(
        chat_id,
        'assistant',
        2,
      )
      if (!chatHistory.includes('http://QwsKvkDN.w.njqzlive.com/p/tVoYx8k5')) {
        await MessageSender.sendById({
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: '直接点链接：http://QwsKvkDN.w.njqzlive.com/p/tVoYx8k5 下单报名就行',
        })
      }
    }

    return actionInfo
  }

  public static async sendCaseImage(
    chat_id: string,
  ): Promise<IActionInfo> {
    const user_id = getUserId(chat_id)

    const searchResult = await YuheSalesCase.searchByChatId(chat_id, [
      YuheSalesCase.peipaoyingDoc,
    ])

    const revenueTmp = await UserSlots.getUserSlotSubTopicContent(chat_id, '基本信息', '年营业额')
    const revenue = revenueTmp === '' ? '20万' : revenueTmp
    const additionInfo = `\n案例信息
学员学习前：${revenue}左右年营业额（营业额数值可以随机一点）
学员学习后：营业额有提升但不要太夸张`
    const actionInfo: IActionInfo = {
      additionInfo: additionInfo,
    }

    if (!searchResult) {
      return actionInfo
    }

    const chatHistory = await ChatHistoryService.formatHistoryOnRole(
      chat_id,
      'assistant',
      5,
    )

    if (chatHistory.includes(`${searchResult.topic}案例1`)) {
      return actionInfo
    }

    const sendImages = async () => {
      await sleep(5000)
      const images = searchResult.images.slice(0, 3)

      for (let i = 0; i < images.length; i++) {
        const image = images[i]
        await sleep(1000)

        await MessageSender.sendById({
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: `[${searchResult.topic}案例${i + 1}]`,
          send_msg: {
            type: IWecomMsgType.Image,
            url: image,
          },
        })
      }
    }

    actionInfo.additionInfo = `\n案例信息：\n${searchResult.description}`
    actionInfo.callback = sendImages

    return actionInfo
  }

  public static async enterPostpone(chat_id: string) {

    logger.trace({ chat_id:chat_id }, '客户执行延期逻辑')


    if ((await ChatStateStore.getFlags<IChattingFlag>(chat_id)).has_postpone) {
      return {
        additionInfo: '\n当前客户已经延期过了，不能再延期',
      } as IActionInfo
    }

    await ChatDB.setStopGroupPush(chat_id, true)

    await ChatStateStore.update(chat_id, {
      state: <IChattingFlag> {
        is_in_postpone: true,
      },
    })

    await ChatStateStore.update(chat_id, { nextStage: YuHeNode.FreeTalk })

    await SilentReAsk.schedule(TaskName.enter_postpone, chat_id, 30 * 60 * 1000, {}, {
      auto_retry: true,
    })

    const currentTime = await YuheDataService.getCurrentTime(chat_id)
    const dayDiff = 5 -  currentTime.day
    const date = DateHelper.add(new Date(), dayDiff, 'day')

    return {
      additionInfo: `\n可供延期时间：${DateHelper.getFormattedDate(date, false)}`
    } as IActionInfo
  }

  public static async reaskAnotherDay(chat_id: string) {

    logger.trace({ chat_id:chat_id }, '客户执行延期逻辑')

    if ((await ChatStateStore.getFlags<IChattingFlag>(chat_id)).has_postpone) {
      return {
        additionInfo: '\n当前客户已经延期过了，不能再延期',
      } as IActionInfo
    }

    await SilentReAsk.schedule(TaskName.reask_another_day, chat_id, 30 * 60 * 1000, {}, {
      auto_retry: true,
    })

    const currentTime = await YuheDataService.getCurrentTime(chat_id)
    const dayDiff = 5 -  currentTime.day
    const date = DateHelper.add(new Date(), dayDiff, 'day')

    return { additionInfo: `\n可供延期时间：${DateHelper.getFormattedDate(date, false)}` } as IActionInfo
  }
}
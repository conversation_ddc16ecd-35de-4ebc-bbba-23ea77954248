/**
 * MetaAction 类用于定义不同销售阶段的“元行为”。
 * 每个 metaActions 集合代表一个阶段，例如课中、课后、卖课等。
 * 元行为是对话策略的基本单元，用于指导 AI 在特定情境下如何回应客户。
 */
export class MetaAction {
  /**
   * 元行为集合 - 不同阶段：
   * - 主要目标：维持课堂专注，引导学习行为。
   * - 使用场景：客户在听课期间提出的问题或讨论。
   * - 填写规则：
   *   - key: 行为名称（中文，动词+名词组合，不超过9个字），修改已有key时要全局查找一下，防止无法匹配下游逻辑
   *   - value: 行为描述，需简洁明了地说明该行为的应用场景与表达方式
   *   - 注意避免提供具体解决方案，只引导方向
   */
  // 元行为：挖需
  public static metaActionIntentionQuery: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习方向但不提供具体解决方案',
    '询问称呼': '请问该怎么称呼？',
    '询问抖音状态': '可以从以下主题中选择一个进行询问：是否已有抖音号、粉丝数量、发布频率、账号类型（企业号or个人号）、是否会拍摄、剪辑视频，内容质量情况（浏览量、点赞互动情况）',
    '深入询问直播问题': '当客户明确表示自己在做抖音直播时可以参考以下主题从中选择一个问题询问：你抖音直播做了多久了？现在直播间粉丝量大概有多少？直播间变现了吗现在？变现了多少？',
    '深入询问短视频问题': '当客户明确表示自己在做抖音短视频时可以参考以下主题从中选择一个问题询问：咱们拍抖音短视频拍了多久了？现在在抖音上能吸引到多少客户到店呢？大概能通过抖音变现了多少呢？',
    '了解抖音相关信息': '当客户不做抖音时，可以延伸询问相关信息，如：你之前对AI有过了解吗？你是从哪里了解的AI线上获客呢？你之前有尝试过拍短视频尝试下吗？你们现在线下店流量怎么样啊？',
    '询问所在行业': '若不清楚客户行业，询问客户行业？',
    '询问所在城市': '咱们是在哪里开的店？',
    '询问获客经历': '当年营业额小于100万的时候可以询问：现在你这个营收算不上很高，之前有尝试过其他手段来获得客流量吗？当客户年营业额大于100万时可以询问：咱们这个你年营收已经挺高的，是之前做过其他营销手段还是已经做过线上获客了？',
    '询问经营现状': '已经开店或以前开过店的客户，可以从以下主题中选择一个询问客户：线下客流占比、美团等线上平台获客占比、是否做私域、最大困扰（如客流量不足、线上流量差、经营成本高）',
    '进行通用诊断': '当你觉得已经足够了解客户时，或者已经问了客户5轮以上的问题，需要根据上下文和已经得到的客户信息对客户的情况进行诊断',
    '简单总结': '当询问过客户多次问题后，此时应该有限给客户一些建议而不是继续进行询问，避免多次询问导致客户反感，总结客户提供的信息并适当给予客户建议',
    '提供建议': '当客户描述想要解决的问题，可以为客户提供解决问题的方向，当不提供具体的解决方案',
    '给予鼓励': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '建立信任': '当客户出现描述个人经营难处时，共情客户经营难点，体现对这个处境很熟悉，提供学习的方向但不提供具体解决方案，建立自己专业靠谱的形象',
    // 测试延期
    '提供延期方案': '客户没提延期上课的话不要使用，当客户明确表示这期没法上课，需要延期上课或更换课程时间时采用，询问客户原因并告知只能延期一次',
  }
  // 元行为：课中
  public static metaActionsDuringCourse: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '引导相关话题': '这个跟我们课程内容无关哈，咱们还是要把注意力放到怎么有效获客上',
    '建立信任': '当客户出现描述个人经营难处时，共情客户经营难点，体现对这个处境很熟悉，提供学习的方向但不提供具体解决方案，建立自己专业靠谱的形象',
    '给予鼓励': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '介绍课程老师': '铺垫中神通老师是自己真正实操过并且被抖音官方认可的抖音获客老师，跟着有结果的老师学习才会有结果',
    '引导进入直播间': '中神通老师正在直播间上课哈，咱们先进去听课，有什么问题课后再说',
  }
  // 元行为：基础，进量 --> 第一课结束
  public static metaActionsAfterAdding: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '推荐直播': '我们的课程是直播课，直播课上跟大家一起互动学习效果最好，既然有学习的决心，就协调好时间按时来上课！如果实在没时间，晚上七点到十点期间记得进直播看个十分钟，打卡签到下，老师帮你去特殊申请回放',
    '引导上课': '是这样的，咱们这个问题老师见的多了，在课上会讲到的哈，一定要来直播间学习（不要说老师会手把手教）',
    '引导相关话题': '这个跟我们课程内容无关哈，咱们还是要把注意力放到怎么有效获客上',
    '询问基本信息': '请问咱们这边怎么称呼呢？请问咱们这边是什么行业呢？',
    '询问线上经验': '咱们有什么做抖音的经验吗',
    '否定错误行为': '咱们不要再这样发视频了哈，这样发没有流量的，把号都发废了',
    '否定套用模版': '课程里面都说了，套模版是没有用的，一定要根据自己的行业情况来定制文案。要是有用，你早就做起来了',
    '否定其他机构': '那是你没有找到有实力，有结果的机构，我们敢送线下课就敢把你们都带起来，不然学员还不得砸公司',
    '发送成功案例': '当客户提出想看历史成功案例的需求或者需要使用成功案例佐证观点时才需要发送，历史发过案例就不再发送，降低频率',
    '建立信任': '当客户出现描述个人经营难处时，共情客户经营难点，体现对这个处境很熟悉，提供学习的方向但不提供具体解决方案，建立自己专业靠谱的形象',
    '给予鼓励': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '询问经营现状': '参考客户画像中的经营现状，可以从以下主题中的一个子项目括号内部选择一个询问客户获课方式（线下客流占比、美团等线上平台获客占比、是否做私域）或核心痛点（最大困扰、客流量不足、线上流量差、经营成本高等）',
    '询问抖音状态': '参考客户画像中的抖音运营状态，可以从以下主题中的一个子项目括号内部选择一个进行询问：客户账号状态（是否已有抖音号、粉丝数量、发布频率、账号类型（企业号or个人号））、内容能力（是否会拍摄、剪辑视频，内容质量情况（浏览量、点赞互动情况））、核心痛点（不知道怎么做、不清楚平台规则、不会做内容、想不出文案）',
    '介绍课程老师': '铺垫中神通老师是自己真正实操过并且被抖音官方认可的抖音获客老师，跟着有结果的老师学习才会有结果',
    // 测试延期
    '提供延期方案': '客户没提延期上课的话不要使用，当客户明确表示这期没法上课，需要延期上课或更换课程时间时采用，询问客户原因并告知只能延期一次',
  }
  // 元行为：第一课结束 --> 第二课卖课
  public static metaActionsAfterCourse1: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '推荐直播': '我们的课程是直播课，直播课上跟大家一起互动学习效果最好，既然有学习的决心，就协调好时间按时来上课！如果实在没时间，晚上七点到十点期间记得进直播看个十分钟，打卡签到下，老师帮你去特殊申请回放',
    '引导上课': '是这样的，咱们这个问题老师见的多了，在课上会讲到的哈，一定要来直播间学习（不要说老师会手把手教）',
    '引导相关话题': '这个跟我们课程内容无关哈，咱们还是要把注意力放到怎么有效获客上',
    '否定错误行为': '咱们不要再这样发视频了哈，这样发没有流量的，把号都发废了',
    '否定套用模版': '课程里面都说了，套模版是没有用的，一定要根据自己的行业情况来定制文案。要是有用，你早就做起来了',
    '否定其他机构': '那是你没有找到有实力，有结果的机构，我们敢送线下课就敢把你们都带起来，不然学员还不得砸公司',
    '发送成功案例': '当客户提出想看历史成功案例的需求或者需要使用成功案例佐证观点时才需要发送，历史发过案例就不再发送，降低频率',
    '铺垫陪跑营': '只有当客户主动提及到陪跑服务，以及提到后续的陪跑营的，可以强化和铺垫这个目标和诉求，继而引导客户关注晚上的直播间的，表示老师应该有优惠价格的',
    '建立信任': '当客户出现描述个人经营难处时，共情客户经营难点，体现对这个处境很熟悉，提供学习的方向但不提供具体解决方案，建立自己专业靠谱的形象',
    '给予鼓励': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '介绍课程老师': '铺垫中神通老师是自己真正实操过并且被抖音官方认可的抖音获客老师，跟着有结果的老师学习才会有结果',
    // 测试延期
    '提供延期方案': '客户没提延期上课的话不要使用，当客户明确表示这期没法上课，需要延期上课或更换课程时间时采用，询问客户原因并告知只能延期一次',
    // 测试回放
    '发送回放': '客户明确说明需要回放时发送，不要说别的，其他时候不要发送，对话记录发过也不要重复发送'
  }
  // 元行为：第二课卖课 --> 第三课结束
  public static metaActionsAfterCourse2: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '给予鼓励': '当客户积极学习课程内容却信心不够时，给予用正确的方法学习后一定拿到结果的信心',
    '拒绝无理要求': '委婉拒绝客户的无理要求，包括但不限于：一直陪跑到回本为止',
    '引导相关话题': '这个跟我们课程内容无关哈，咱们还是要把注意力放到怎么有效盈利上',
    '否定套用模版': '课程里面都说了，套模版是没有用的，一定要根据自己的行业情况来定制文案。要是有用，你早就做起来了',
    '展示实力自信': '那是你没有找到有实力，有结果的机构，我们敢送线下课就敢把你们都带起来，不然学员还不得砸公司',
    '发送成功案例': '当客户提出想看历史成功案例的需求或者需要使用成功案例佐证观点时才需要发送，历史发过案例就不再发送，降低频率',
    '放大竞争焦虑': '你不学，同行学了就是弯道超车，到时候流量也好，赚钱也好，都轮不到你，后悔也晚了',
    // 陪跑营
    '介绍陪跑营价值': '陪跑营不仅是学知识，更是实操与结果的兑现。通过6V1团队支持和专业指导，帮助你从“知道”到“做到”，快速落地抖音获客并实现盈利',
    '强调落地实操': '速成班只是让你了解抖音，而陪跑营是帮助你真正做到，从落地执行到获取流量、转化盈利，每一步都在专业的指导下，确保你能快速看到效果',
    '否定自己摸索': '自己摸索不仅浪费时间，效果也不明显。你再怎么尝试，结果总是差强人意，而陪跑营提供的是专业团队手把手的指导，确保你不走弯路',
    '否定代运营': '代运营能做的事有限，很多代运营机构的水平参差不齐，花了钱却收效甚微。真正能带你走到成功的是陪跑营的个性化定制方案和AI工具支持，确保每一步都在正确轨道上',
    '否定其他课程': '很多课程都很理论，但陪跑营的优势在于它会根据你的具体行业情况，为你定制实战方案，帮助你解决实操中遇到的具体问题，迅速落地',
    '介绍陪跑营内容': '陪跑营包含30天中神通老师亲自授课，365天支持答疑，赠送数字人分身、无人直播、百叶钩子文案，包含3天2夜AI实体流量超级门店设计的线下课',
    '发起购买邀约': '在判断客户意愿强烈、痛点突出且信任基础已建立时，清晰地发出付费报名邀请，提供报名方式或指引',
  }
  // 元行为：销售，第三课结束 --> 买课/流失
  public static metaActionsAfterCourse3: Record<string, string> = {
    // 基础
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '给予鼓励': '当客户课程进度较慢或信心不够时，给予用正确的方法学习后一定拿到结果的信心',
    '拒绝无理要求': '委婉拒绝客户的无理要求，包括但不限于：一直陪跑到回本为止',
    '引导相关话题': '这个跟我们课程内容无关哈，咱们还是要把注意力放到怎么有效获客并盈利',
    '明确价值': '基于客户的生意与其明确应用AI辅助抖音流量获客的实操方案以及ROI价值',
    '询问想法': '如果在客户画像没有明确的痛点需求或目标时，可以询问客户想法，比如：你是不是觉得现在做抖音没必要？',
    '询问理解情况': '如果客户对抖音流量获客、AI工具没有明确的了解，可以结合客户情况询问其当前对课程理解的知识盲区',
    // 进阶
    '询问下单卡点': '若客户下单卡点不明确或者回应模糊时，可结合客户画像询问，比如：你是不是担心很难操作坚持不下来，还是担心成交之后我们就不好好服务了',
    '渲染自己摸索难度': '若客户想自己探索流量变现，可以适当接地气的结合客户情况分享下实操思路，展现专业性，进而表示自己摸索不是不行，难度极大，耗费心力很有可能没有结果，还会遇到很多挑战',
    '展示实力自信': '那是你没有找到有实力，有结果的机构，我们敢送线下课就敢把你们都带起来，不然学员还不得砸公司',
    '否定其他课程': '很多课程都很理论，但我们的优势在于它会根据你的具体行业情况，为你定制实战方案，帮助你解决实操中遇到的具体问题，迅速落地',
    '否定代运营': '代运营能做的事有限，很多代运营机构的水平参差不齐，花了钱却收效甚微。真正能带你走到成功的是个性化定制方案和AI工具支持，确保每一步都在正确轨道上',
    '发送成功案例': '当客户提出想看历史成功案例的需求或者需要使用成功案例佐证观点时才需要发送，历史发过案例就不再发送，降低频率',
    '打击犹豫心态': '机会不是等来的，犹豫的人注定被淘汰。就这么点小事都决不下来，还怎么指望自己带起一个账号？',
    '放大竞争焦虑': '你不学，同行学了就是弯道超车，到时候流量也好，赚钱也好，都轮不到你，后悔也晚了',
    // 陪跑营
    '介绍陪跑营服务': '陪跑营的话是6个专业的老师手把手教你一个肯定效率就上来了。这个多人陪跑其实也不错，就是对你来说还是要自己多花时间学习吸收，不是针对性的。陪跑是老师针对你的自身情况，以及你的行业来给你制定课程，这样针对性的从基础开始教你',
    '介绍陪跑营内容': '陪跑营包含30天中神通老师亲自授课，365天支持答疑，赠送数字人分身、无人直播、百叶钩子文案，包含3天2夜AI实体流量超级门店设计的线下课',
    '介绍陪跑营价值': '陪跑营不仅是学知识，更是实操与结果的兑现。通过6V1团队支持和专业指导，帮助你从“知道”到“做到”，快速落地抖音获客并实现盈利',
    '强调陪跑营优势': '速成班只是让你了解抖音，而陪跑营是帮助你真正做到，从落地执行到获取流量、转化盈利，每一步都在专业的指导下，确保你能快速看到效果，陪跑营有30天中神通团队6对1陪跑，确保你在短时间内能够快速掌握并运用技巧。加上365天答疑支持，整个过程都有专业老师全程陪跑，消除所有疑虑',
    '强调线下课差异化': '除了线上陪跑之外，我们还送3天2夜的线下实操，有什么卡点，现场实操；我们的老师参与过抖音平台规则的制定，能给你最前沿、最切合实际的营销方案，确保你获得最新的实操技能，回去就能开始赚',
    '强推陪跑营服务': '当客户对陪跑营很有意向，非常希望了解具体内容的时候使用，慎重使用',
    '提供定金方案': '当客户不断强调自己目前有资金问题或缺钱才能使用这个行为，主动提出可以先付定金，后面补齐，客户第一次提的时候不要用这个行为',
    '发起购买邀约': '在判断客户意愿强烈、痛点突出且信任基础已建立时，清晰地发出付费报名邀请，提供报名方式或指引',
  }
  // 元行为：售后
  public static metaActionsAfterPaid: Record<string, string> = {
    '回答问题': '跟课程相关问题，引导客户去陪跑群找陪跑老师沟通，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '建立信任': '当客户出现描述个人经营难处时，共情客户经营难点，体现对这个处境很熟悉，提供学习的方向但不提供具体解决方案，建立自己专业靠谱的形象',
    '给予鼓励': '当客户积极学习课程内容却信心不够时，给予用正确的方法学习后一定拿到结果的信心',
    '询问原因': '在客户表达退费或者取消报名意愿时，询问客户退费的原因',
    '坚定挽留': '在客户表达退费或者取消报名意愿时，要坚定挽留，可以通过客户画像结合课程价值扬长避短，求同存异，可适度反驳客户',
    '介绍课程老师': '铺垫中神通老师是自己真正实操过并且被抖音官方认可的抖音获客老师，跟着有结果的老师学习才会有结果',
  }
  // 元行为：延期
  public static metaActionsDeferCourse: Record<string, string> = {
    '退出延期': '如果客户说不延期了，或者聊跟课程延期无关的话题，不再提延期的事情，就使用这个行为',
    '确认延期原因': '如果客户没有说明想要延期的原因，则追问一下延期原因',
    '澄清课程时间': '如果客户晚上7点没时间上课，则告知客户我们的上课时间是固定的，并询问是否同意延期',
    '执行延期': '告知客户已经将课程延期，并督促到时一定来上课',
  }
  // 思考Prompt：最顶层的思想，不同阶段采用不同的思考内核，包括如何调度元行为，如何适配客户现状，如何引导策略输出等，要求尽量简洁，与业务解耦，给模型最大的发挥空间
  public static thinkPromptIntentionQuery = '参考对话记录，客户画像与客户行为，思考如何才能使用元行为获取客户信息，请使用最合适的元行为，不超过1个。你需要参考以下优先级，回答问题，简单总结，询问称呼，询问所在行业，询问经营现状，询问抖音状态，询问获客手段，询问抖音相关问题，通用诊断。注意保持策略简洁，每次只提一个问题。注意连续询问问题不应连续超过两次，应该在连续询问问题后进行简单总结'
  public static thinkPromptDuringCourse = '参考对话记录，客户画像与客户行为，思考如何才能使用元行为引导客户现在就去上课，请使用最合适的元行为，不超过1个'
  public static thinkPromptAfterAdding = '参考对话记录，客户画像与客户行为，思考如何才能使用元行为增强信任，加深了解，提高客户对课程的期待，请使用最合适的元行为，不超过2个，也可以1个'
  public static thinkPromptAfterCourse1 = '参考对话记录，客户画像与客户行为，思考如何才能使用元行为加深了解，渗透课程价值，铺垫中神通老师实力，请使用最合适的元行为，不超过2个，也可以1个'
  public static thinkPromptAfterCourse2 = '参考对话记录，客户画像与客户行为，思考如何才能使用元行为增强信任，渗透课程价值，请使用最合适的元行为，不超过2个，也可以1个，只给对陪跑营有强烈意向的客户推进销售陪跑营进程，其他客户则不要提及，可以适当配合发送案例'
  public static thinkPromptAfterCourse3 = '参考对话记录，客户画像与客户行为，思考如何才能使用元行为挖掘顾虑，解决异议，深化价值理解，推进销售陪跑营进程，请使用最合适的元行为，不超过2个，也可以1个。客户正在参加速成班，课程结束后还有陪跑营课程，可以适当配合发送案例'
  public static thinkPromptAfterPaid = '参考对话记录，客户画像与客户行为，思考如何才能使用元行为推进对话，请使用最合适的元行为，不超过2个，也可以1个。客户已经购买，无需继续推销，正常回应即可；若客户想退款，一定不能同意，想尽一切办法挽留'
  public static thinkPromptDeferCourse = '参考对话记录，客户画像与客户行为，思考如何才能使用元行为推进对话，请使用最合适的元行为，不超过1个'
}
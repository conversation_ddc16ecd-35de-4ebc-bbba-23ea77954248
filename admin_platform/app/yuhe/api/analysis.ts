'use server'

import { PrismaMongoClient } from '../../../../packages/model/mongodb/prisma'
import { IChattingFlag } from '../../../../apps/yuhe/state/user_flags'
import { AnalysisData } from '@/app/type/analysis'
import { contentWithFrequency } from '../../../../packages/service/local_cache/type'
import { UserSlots } from '../../../../packages/service/user_slots/extract_user_slots'
import { isFillAnyUserSlots, isFillUserSlots } from '../../../../apps/yuhe/visualized_sop/visualized_sop_variable'
import { YuheDataService } from '../../../../apps/yuhe/helper/getter/get_data'
import dayjs from 'dayjs'

export async function getAnalysisData(startCourseNo:number, endCourseNo:number):Promise<AnalysisData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const getChatInfo = async(startCourseNo:number, endCourseNo:number) => {
    return await mongoClient.chat.findMany({ where:{ OR:[{ course_no:{ gte:startCourseNo, lte:endCourseNo } }, { course_no_ori:{ gte:startCourseNo, lte:endCourseNo } }]  }, select:{ id:true, wx_id:true, contact:true, chat_state:true, course_no:true, course_no_ori:true, phone:true, ip:true } })
  }
  const getAccounts = async() => {
    const mongoConfigClient = PrismaMongoClient.getConfigInstance()
    return await mongoConfigClient.config.findMany({ where:{ enterpriseName:'yuhe' }, select:{ accountName:true, wechatId:true } })
  }

  const [chatInfo, accounts] = await Promise.all([getChatInfo(startCourseNo, endCourseNo), getAccounts()])
  const accountMap = new Map<string, string>()
  for (const account of accounts) {
    accountMap.set(account.wechatId, account.accountName)
  }
  const filtedChatInfo = chatInfo.filter((item) => item.wx_id != '****************')

  const chatHistory = await mongoClient.chat_history.findMany({ where:{ chat_id:{ in:chatInfo.map((item) => item.id) }, role:'user' }, select:{ chat_id:true, id:true } })
  const chatEventTrack = await mongoClient.event_track.findMany({ where:{ chat_id:{ in:chatInfo.map((item) => item.id) }, type:'完成支付' } })
  const paidTimeMap = new Map<string, Date>()
  for (const event of chatEventTrack) {
    paidTimeMap.set(event.chat_id, event.timestamp)
  }

  const ipTable = await mongoClient.ip_table.findMany({ where:{ OR:[{ start_course_no:{ lte:endCourseNo } }, { end_course_no:{ gte:startCourseNo } }] } })

  return filtedChatInfo.filter((chat) => !['****************', '****************', '****************', '****************', '****************'].includes(chat.contact.wx_id)).map((chat) => {
    const state = chat.chat_state.state as IChattingFlag
    const userSlots = UserSlots.fromRecord(chat.chat_state.userSlots as Record<string, contentWithFrequency>)
    return {
      chatId:chat.id,
      name: chat.contact.wx_name,
      phone: chat.phone ?? '',
      ip:chat.ip ?? ipTable.find((item) => item.account == chat.wx_id && item.start_course_no <= (chat.course_no_ori ?? chat.course_no ?? 0) && (chat.course_no_ori ?? chat.course_no ?? 0) <= item.end_course_no)?.ip ?? '',
      paidTime:paidTimeMap.get(chat.id) ?? null,
      isInviteGroupFailAfterPayment:state.is_invite_group_fail_after_payment ?? false,
      assistant: accountMap.get(chat.wx_id) ?? '未知',
      chatNumber: chatHistory.filter((item) => item.chat_id == chat.id).length,
      isPaid:state.is_complete_payment ?? false,
      courseNo: chat.course_no ?? 0,
      courseNoOri:chat.course_no_ori ?? chat.course_no ?? 0,
      isDelay: chat.course_no_ori != null,
      isFillAnyUserSlots:isFillAnyUserSlots(userSlots),
      isFillUserSlots:isFillUserSlots(userSlots),
      isCompleteDouyinAnalysis:state.is_complete_douyin_analysis ?? false,
      isCompleteHomework1: state.is_complete_homework1 ?? false,
      isCompleteHomework2: state.is_complete_homework2 ?? false,
      isCompleteHomework3: state.is_complete_homework3 ?? false,
      isAttendCourseDay1: state.is_attend_course_day1 ?? false,
      isAttendCourseDay2: state.is_attend_course_day2 ?? false,
      isAttendCourseDay3: state.is_attend_course_day3 ?? false,
      isAttendCourseDay4: state.is_attend_course_day4 ?? false,
      isCompleteCourseDay1: state.is_complete_course_day1 ?? false,
      isCompleteCourseDay2: state.is_complete_course_day2 ?? false,
      isCompleteCourseDay3: state.is_complete_course_day3 ?? false,
      isCompleteCourseDay4: state.is_complete_course_day4 ?? false,
    }
  })
}

export async function updateAttendAndCompleteCourseData() {
  const threeDaysAgo = Number(dayjs().subtract(3, 'day').format('YYYYMMDD'))
  console.log(threeDaysAgo)
  const mongoClient = PrismaMongoClient.getInstance()
  const users = (await mongoClient.chat.findMany({ where:{ course_no:{ gte:threeDaysAgo } } })).filter((user) => user.phone)
  for (let i = 0; i < users.length; i += 10) {
    await Promise.allSettled(users.slice(i, i + 10).map((user) => Promise.allSettled([
      YuheDataService.isAttendCourse(user.id, 1),
      YuheDataService.isAttendCourse(user.id, 2),
      YuheDataService.isAttendCourse(user.id, 3),
      YuheDataService.isAttendCourse(user.id, 4),
      YuheDataService.isCompletedCourse(user.id, 1),
      YuheDataService.isCompletedCourse(user.id, 2),
      YuheDataService.isCompletedCourse(user.id, 3),
      YuheDataService.isCompletedCourse(user.id, 4),
    ])))
  }
}
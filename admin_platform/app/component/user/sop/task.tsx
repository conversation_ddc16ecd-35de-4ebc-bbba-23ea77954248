import Link from 'next/link'
import { ITask } from '../../../../../packages/service/visualized_sop/visualized_sop_type'
import dayjs from 'dayjs'

export default function Task({ task }:{task:ITask}) {
  const data = task
  return <tr>
    <td>{data.name}</td>
    <td>{data.scheduleTime.week}</td>
    <td>{data.scheduleTime.day}</td>
    <td>{data.scheduleTime.time}</td>
    <td>{dayjs(data.sendTime).format('YYYY-MM-DD HH:mm:ss')}</td>
    <td>{data.force}</td>
    <td>
      <Link href={`../../sop/${data.name}`}><button className='btn btn-info btn-soft'>detail</button></Link>
    </td>
  </tr>
}